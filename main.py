import pandas as pd

# Read the CSV files
print("Reading fin.csv and crs.csv files...")
fin_df = pd.read_csv('fin.csv')
crs_df = pd.read_csv('crs.csv')

print("Successfully loaded both files as pandas DataFrames!")
print("="*50)

print("fin.csv DataFrame:")
print(f"Shape: {fin_df.shape}")
print(f"Columns: {fin_df.columns.tolist()}")
print(f"First 5 rows:")
print(fin_df.head())
print(f"Data types:")
print(fin_df.dtypes)

print("\n" + "="*50)
print("crs.csv DataFrame:")
print(f"Shape: {crs_df.shape}")
print(f"Columns: {crs_df.columns.tolist()}")
print(f"First 5 rows:")
print(crs_df.head())
print(f"Data types:")
print(crs_df.dtypes)

print("\n" + "="*50)
print("Data Quality Check:")
print(f"Missing values in fin.csv: {fin_df.isnull().sum().sum()}")
print(f"Missing values in crs.csv: {crs_df.isnull().sum().sum()}")

print(f"\nUnique values in fin.csv: {fin_df.nunique().iloc[0]}")
print(f"Unique values in crs.csv: {crs_df.nunique().iloc[0]}")

print("\n" + "="*50)
print("DataFrame Info:")
print("\nfin.csv DataFrame info:")
fin_df.info()

print("\ncrs.csv DataFrame info:")
crs_df.info()